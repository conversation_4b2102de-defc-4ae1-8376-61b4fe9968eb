#!/bin/zsh

p="${PWD##*/}"

if [[ -d .git && ! -L .git ]]; then
  d="$HOME/Documents/Git/$p"
  echo "Moving .git to $d"
  mkdir -p "$d"

  # Remove destination directory if it exists
  [[ -d "$d/.git" ]] && rm -rf "$d/.git"

  mv .git "$d"
  ln -s "$d/.git" .git
  echo ""
fi

if [[ "$1" == "--nono" ]]; then
  # Delete all pnpm-lock.yaml files
  find . -name "pnpm-lock.yaml" -type f -exec rm -v {} \;

  # For each node_modules directory:
  find . -type d -name "node_modules" -prune | sort | while read -r dir; do
    echo "Deleting: $dir/*"
    # Clear contents but keep the directory
    find "$dir" -mindepth 1 -maxdepth 1 -exec rm -rf {} \;
  done

  # For each node_modules symlink:
  find . -type l -name "node_modules" -prune | sort | while read -r dir; do
    echo "Deleting symlink: $dir"
    rm "$dir"
  done
  echo ""
fi

# For each package.json, ensure node_modules/.pnpm exists and is ignored by Dropbox
find . -name "package.json" -not -path "*/node_modules/*" | while read -r rpkg; do
  echo "$(dirname "$rpkg")\t$rpkg"
done | sort -k1,1 | cut -f2- | while read -r pkg; do
  # Get the directory of the package.json
  dir=$(dirname "$pkg")
  dirn=$dir/node_modules
  if [[ "$dir" == "." ]]; then
    echo "root:"
  else
    echo "$dir:"
  fi
  echo "  node_modules/.pnpm"
  mkdir -p "$dirn/.pnpm"
  xattr -w com.dropbox.ignored 1 "$dirn"
  echo "  com.dropbox.ignored: $(xattr -p com.dropbox.ignored $dirn)"
  echo ""
done
